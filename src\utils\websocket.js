/**
 * WebSocket管理类
 * 支持多个WebSocket连接的创建、管理和消息处理
 */
class WebSocketManager {
  constructor() {
    this.connections = new Map() // 存储所有连接
    this.messageHandlers = new Map() // 存储消息处理器
    this.reconnectAttempts = new Map() // 重连尝试次数
    this.reconnectTimers = new Map() // 重连定时器
    this.connectionConfigs = new Map() // 存储连接配置以便重连
    this.connectingStates = new Map() // 标记正在连接中的状态
    this.maxReconnectAttempts = 0 // 无限重连（设为0表示无限）
    this.reconnectInterval = 3000 // 重连间隔(ms)
    this.maxReconnectInterval = 30000 // 最大重连间隔(ms)
    this.isDestroyed = false // 标记是否已销毁
  }

  /**
   * 创建WebSocket连接
   * @param {string} key - 连接的唯一标识
   * @param {string} url - WebSocket连接地址
   * @param {Object} options - 配置选项
   */
  connect(key, url, options = {}) {
    if (this.isDestroyed) {
      console.warn('WebSocket管理器已销毁，无法创建新连接')
      return null
    }

    // 检查是否已经在连接中，避免重复连接
    if (this.connectingStates.get(key)) {
      console.log(`WebSocket [${key}] 正在连接中，跳过重复连接`)
      return null
    }

    const existingWs = this.connections.get(key)
    if (existingWs && (existingWs.readyState === WebSocket.CONNECTING || existingWs.readyState === WebSocket.OPEN)) {
      console.log(`WebSocket [${key}] 已存在连接，跳过重复连接`)
      return existingWs
    }

    // 如果已存在连接，先关闭
    if (this.connections.has(key)) {
      const ws = this.connections.get(key)
      if (ws && ws.readyState !== WebSocket.CLOSED) {
        ws.close(1000, '重新连接')
      }
      this.connections.delete(key)
    }

    try {
      // 标记正在连接中
      this.connectingStates.set(key, true)

      const ws = new WebSocket(url)
      this.connections.set(key, ws)
      this.reconnectAttempts.set(key, 0)
      // 保存连接配置以便重连
      this.connectionConfigs.set(key, { url, options })

      // 连接成功
      ws.onopen = (event) => {
        console.log(`WebSocket连接成功: ${key} -> ${url}`)
        this.reconnectAttempts.set(key, 0) // 重置重连次数
        this.connectingStates.delete(key) // 清除连接中状态
        if (options.onOpen) {
          options.onOpen(event, key)
        }
      }

      // 接收消息
      ws.onmessage = (event) => {
        console.log(`WebSocket收到消息 [${key}]:`, event.data)
        try {
          const data = JSON.parse(event.data)
          this.handleMessage(key, data)
          if (options.onMessage) {
            options.onMessage(data, key)
          }
        } catch (error) {
          console.error(`WebSocket消息解析失败 [${key}]:`, error)
          // 如果JSON解析失败，尝试处理纯文本消息
          this.handleMessage(key, { text: event.data })
          if (options.onMessage) {
            options.onMessage({ text: event.data }, key)
          }
        }
      }

      // 连接关闭
      ws.onclose = (event) => {
        console.log(`WebSocket连接关闭: ${key}`, event.code, event.reason)
        this.connectingStates.delete(key) // 清除连接中状态
        if (options.onClose) {
          options.onClose(event, key)
        }
        // 只有非正常关闭才自动重连
        if (!this.isDestroyed && event.code !== 1000) {
          this.attemptReconnect(key, url, options)
        }
      }

      // 连接错误
      ws.onerror = (error) => {
        console.error(`WebSocket连接错误 [${key}]:`, error)
        this.connectingStates.delete(key) // 清除连接中状态
        if (options.onError) {
          options.onError(error, key)
        }
      }

      return ws
    } catch (error) {
      console.error(`WebSocket创建失败 [${key}]:`, error)
      this.connectingStates.delete(key) // 清除连接中状态
      return null
    }
  }

  /**
   * 断开WebSocket连接
   * @param {string} key - 连接标识
   * @param {boolean} preserveHandlers - 是否保留消息处理器
   */
  disconnect(key, preserveHandlers = false) {
    const ws = this.connections.get(key)
    if (ws) {
      ws.close(1000, '正常关闭')
      this.connections.delete(key)
      
      // 只有在不需要保留处理器时才清除
      if (!preserveHandlers) {
        this.messageHandlers.delete(key)
      }
      
      this.reconnectAttempts.delete(key)
      this.connectionConfigs.delete(key)
      // 清理重连定时器
      this.clearReconnectTimer(key)
      console.log(`WebSocket连接已断开: ${key}`)
    }
  }

  /**
   * 发送消息
   * @param {string} key - 连接标识
   * @param {Object|string} message - 要发送的消息
   */
  send(key, message) {
    const ws = this.connections.get(key)
    if (ws && ws.readyState === WebSocket.OPEN) {
      const data = typeof message === 'string' ? message : JSON.stringify(message)
      ws.send(data)
      console.log(`WebSocket发送消息 [${key}]:`, data)
      return true
    } else {
      console.warn(`WebSocket连接不可用 [${key}], 状态:`, ws ? ws.readyState : '连接不存在')
      return false
    }
  }

  /**
   * 注册消息处理器
   * @param {string} key - 连接标识
   * @param {Function} handler - 消息处理函数
   */
  onMessage(key, handler) {
    if (!this.messageHandlers.has(key)) {
      this.messageHandlers.set(key, [])
    }
    this.messageHandlers.get(key).push(handler)
    console.log(`已注册消息处理器 [${key}], 总数:`, this.messageHandlers.get(key).length)
  }

  /**
   * 移除消息处理器
   * @param {string} key - 连接标识
   * @param {Function} handler - 要移除的处理函数（可选）
   */
  removeMessageHandler(key, handler = null) {
    if (this.messageHandlers.has(key)) {
      if (handler) {
        const handlers = this.messageHandlers.get(key)
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      } else {
        this.messageHandlers.delete(key)
      }
    }
  }

  /**
   * 处理接收到的消息
   * @param {string} key - 连接标识
   * @param {Object} data - 消息数据
   */
  handleMessage(key, data) {
    const handlers = this.messageHandlers.get(key)
    if (handlers && handlers.length > 0) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`消息处理器执行错误 [${key}]:`, error)
        }
      })
    } else {
      console.log(`没有注册的消息处理器 [${key}]`)
    }
  }

  /**
   * 尝试重连
   * @param {string} key - 连接标识
   * @param {string} url - 连接地址
   * @param {Object} options - 配置选项
   */
  attemptReconnect(key, url, options) {
    if (this.isDestroyed) {
      return
    }

    const attempts = this.reconnectAttempts.get(key) || 0

    // 无限重连模式（maxReconnectAttempts为0）或未达到最大重连次数
    if (this.maxReconnectAttempts === 0 || attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(key, attempts + 1)

      // 计算重连间隔，使用指数退避算法，但不超过最大间隔
      const backoffInterval = Math.min(
        this.reconnectInterval * Math.pow(1.5, Math.min(attempts, 10)), // 最多10次后保持最大间隔
        this.maxReconnectInterval
      )

      const maxAttemptsText = this.maxReconnectAttempts === 0 ? '∞' : this.maxReconnectAttempts
      console.log(`WebSocket重连尝试 [${key}]: ${attempts + 1}/${maxAttemptsText}, 延迟: ${backoffInterval}ms`)

      // 清理之前的重连定时器
      this.clearReconnectTimer(key)

      const timer = setTimeout(() => {
        if (!this.isDestroyed && (!this.connections.has(key) || this.connections.get(key).readyState === WebSocket.CLOSED)) {
          console.log(`开始重连 [${key}]...`)
          this.connect(key, url, options)
        }
        this.reconnectTimers.delete(key)
      }, backoffInterval)

      this.reconnectTimers.set(key, timer)
    } else {
      console.error(`WebSocket重连失败，已达到最大重连次数 [${key}]: ${this.maxReconnectAttempts}`)
    }
  }

  /**
   * 清理重连定时器
   * @param {string} key - 连接标识
   */
  clearReconnectTimer(key) {
    const timer = this.reconnectTimers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.reconnectTimers.delete(key)
      console.log(`已清理重连定时器 [${key}]`)
    }
  }

  /**
   * 获取连接状态
   * @param {string} key - 连接标识
   * @returns {number} WebSocket状态
   */
  getState(key) {
    const ws = this.connections.get(key)
    return ws ? ws.readyState : WebSocket.CLOSED
  }

  /**
   * 检查连接是否打开
   * @param {string} key - 连接标识
   * @returns {boolean}
   */
  isConnected(key) {
    return this.getState(key) === WebSocket.OPEN
  }

  /**
   * 获取连接状态文本
   * @param {string} key - 连接标识
   * @returns {string}
   */
  getStateText(key) {
    const state = this.getState(key)
    const stateMap = {
      [WebSocket.CONNECTING]: '连接中',
      [WebSocket.OPEN]: '已连接',
      [WebSocket.CLOSING]: '关闭中',
      [WebSocket.CLOSED]: '已关闭'
    }
    return stateMap[state] || '未知状态'
  }

  /**
   * 获取所有连接的状态
   * @returns {Object}
   */
  getAllStates() {
    const states = {}
    this.connections.forEach((ws, key) => {
      states[key] = {
        state: ws.readyState,
        stateText: this.getStateText(key),
        isConnected: this.isConnected(key)
      }
    })
    return states
  }

  /**
   * 手动重连指定连接
   * @param {string} key - 连接标识
   */
  reconnect(key) {
    const config = this.connectionConfigs.get(key)
    if (config) {
      console.log(`手动重连 [${key}]...`)
      // 先清理重连定时器，避免与自动重连冲突
      this.clearReconnectTimer(key)
      // 重置重连次数
      this.reconnectAttempts.set(key, 0)
      // 断开现有连接
      const ws = this.connections.get(key)
      if (ws && ws.readyState !== WebSocket.CLOSED) {
        ws.close(1000, '手动重连')
      }
      this.connections.delete(key)
      // 立即重新连接
      this.connect(key, config.url, config.options)
    } else {
      console.warn(`无法重连 [${key}]: 未找到连接配置`)
    }
  }

  /**
   * 手动重连所有连接
   */
  reconnectAll() {
    console.log('手动重连所有WebSocket连接...')
    // 先获取所有配置的副本，避免在遍历过程中修改原集合
    const configs = new Map(this.connectionConfigs)
    configs.forEach((config, key) => {
      this.reconnect(key)
    })
  }

  /**
   * 断开所有连接
   */
  disconnectAll() {
    console.log('正在断开所有WebSocket连接...')
    // 清理所有重连定时器
    this.reconnectTimers.forEach((timer, key) => {
      this.clearReconnectTimer(key)
    })
    this.connections.forEach((ws, key) => {
      this.disconnect(key)
    })
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.isDestroyed = true
    this.disconnectAll()
    this.messageHandlers.clear()
    this.reconnectAttempts.clear()
    this.reconnectTimers.clear()
    this.connectionConfigs.clear()
    this.connectingStates.clear()
    console.log('WebSocket管理器已销毁')
  }
}

// 创建全局实例
const wsManager = new WebSocketManager()

// 确保页面刷新时正确清理连接
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    wsManager.destroy()
  })
}

export default wsManager 
