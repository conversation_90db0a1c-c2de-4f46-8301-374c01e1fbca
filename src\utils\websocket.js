/**
 * WebSocket管理类
 * 支持多个WebSocket连接的创建、管理和消息处理
 * 包含心跳检测、自动重连、连接状态监控等功能
 */
class WebSocketManager {
  constructor() {
    this.connections = new Map() // 存储所有连接
    this.messageHandlers = new Map() // 存储消息处理器
    this.reconnectAttempts = new Map() // 重连尝试次数
    this.reconnectTimers = new Map() // 重连定时器
    this.connectionConfigs = new Map() // 存储连接配置以便重连
    this.connectingStates = new Map() // 标记正在连接中的状态
    this.heartbeatTimers = new Map() // 心跳定时器
    this.heartbeatIntervals = new Map() // 心跳间隔配置
    this.lastHeartbeatTime = new Map() // 最后心跳时间
    this.connectionQuality = new Map() // 连接质量统计
    this.eventListeners = new Map() // 事件监听器
    this.maxReconnectAttempts = 0 // 无限重连（设为0表示无限）
    this.reconnectInterval = 3000 // 重连间隔(ms)
    this.maxReconnectInterval = 30000 // 最大重连间隔(ms)
    this.defaultHeartbeatInterval = 30000 // 默认心跳间隔(ms)
    this.heartbeatTimeout = 10000 // 心跳超时时间(ms)
    this.isDestroyed = false // 标记是否已销毁
  }

  /**
   * 添加事件监听器
   * @param {string} event - 事件名称 (connect, disconnect, reconnect, error, heartbeat)
   * @param {Function} listener - 监听器函数
   */
  addEventListener(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(listener)
  }

  /**
   * 移除事件监听器
   * @param {string} event - 事件名称
   * @param {Function} listener - 监听器函数
   */
  removeEventListener(event, listener) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(listener)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {Object} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event)
    if (listeners && listeners.length > 0) {
      listeners.forEach(listener => {
        try {
          listener(data)
        } catch (error) {
          console.error(`事件监听器执行错误 [${event}]:`, error)
        }
      })
    }
  }

  /**
   * 创建WebSocket连接
   * @param {string} key - 连接的唯一标识
   * @param {string} url - WebSocket连接地址
   * @param {Object} options - 配置选项
   */
  connect(key, url, options = {}) {
    if (this.isDestroyed) {
      console.warn('WebSocket管理器已销毁，无法创建新连接')
      return null
    }

    // 检查是否已经在连接中，避免重复连接
    if (this.connectingStates.get(key)) {
      console.log(`WebSocket [${key}] 正在连接中，跳过重复连接`)
      return null
    }

    const existingWs = this.connections.get(key)
    if (existingWs && (existingWs.readyState === WebSocket.CONNECTING || existingWs.readyState === WebSocket.OPEN)) {
      console.log(`WebSocket [${key}] 已存在连接，跳过重复连接`)
      return existingWs
    }

    // 如果已存在连接，先关闭
    if (this.connections.has(key)) {
      const ws = this.connections.get(key)
      if (ws && ws.readyState !== WebSocket.CLOSED) {
        ws.close(1000, '重新连接')
      }
      this.connections.delete(key)
    }

    try {
      // 标记正在连接中
      this.connectingStates.set(key, true)

      const ws = new WebSocket(url)
      this.connections.set(key, ws)
      this.reconnectAttempts.set(key, 0)
      // 保存连接配置以便重连
      this.connectionConfigs.set(key, { url, options })

      // 连接成功
      ws.onopen = (event) => {
        console.log(`WebSocket连接成功: ${key} -> ${url}`)
        this.reconnectAttempts.set(key, 0) // 重置重连次数
        this.connectingStates.delete(key) // 清除连接中状态

        // 初始化连接质量统计
        this.connectionQuality.set(key, {
          connectTime: Date.now(),
          messageCount: 0,
          errorCount: 0,
          reconnectCount: 0,
          lastMessageTime: Date.now()
        })

        // 启动心跳检测
        this.startHeartbeat(key, options.heartbeatInterval || this.defaultHeartbeatInterval)

        // 触发连接事件
        this.emit('connect', { key, url, event })

        if (options.onOpen) {
          options.onOpen(event, key)
        }
      }

      // 接收消息
      ws.onmessage = (event) => {
        console.log(`WebSocket收到消息 [${key}]:`, event.data)

        // 更新连接质量统计
        const quality = this.connectionQuality.get(key)
        if (quality) {
          quality.messageCount++
          quality.lastMessageTime = Date.now()
          this.lastHeartbeatTime.set(key, Date.now()) // 更新心跳时间
        }

        try {
          const data = JSON.parse(event.data)

          // 检查是否是心跳响应
          if (data.type === 'heartbeat' || data.type === 'pong') {
            console.log(`收到心跳响应 [${key}]`)
            this.emit('heartbeat', { key, type: 'response', data })
            return
          }

          this.handleMessage(key, data)
          if (options.onMessage) {
            options.onMessage(data, key)
          }
        } catch (error) {
          console.error(`WebSocket消息解析失败 [${key}]:`, error)

          // 更新错误统计
          if (quality) {
            quality.errorCount++
          }

          // 如果JSON解析失败，尝试处理纯文本消息
          this.handleMessage(key, { text: event.data })
          if (options.onMessage) {
            options.onMessage({ text: event.data }, key)
          }
        }
      }

      // 连接关闭
      ws.onclose = (event) => {
        console.log(`WebSocket连接关闭: ${key}`, event.code, event.reason)
        this.connectingStates.delete(key) // 清除连接中状态

        // 停止心跳检测
        this.stopHeartbeat(key)

        // 更新连接质量统计
        const quality = this.connectionQuality.get(key)
        if (quality && event.code !== 1000) {
          quality.reconnectCount++
        }

        // 触发断开连接事件
        this.emit('disconnect', {
          key,
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean,
          quality: quality
        })

        if (options.onClose) {
          options.onClose(event, key)
        }

        // 只有非正常关闭才自动重连
        if (!this.isDestroyed && event.code !== 1000) {
          console.warn(`WebSocket异常断开 [${key}], 代码: ${event.code}, 原因: ${event.reason || '未知'}`)
          this.attemptReconnect(key, url, options)
        }
      }

      // 连接错误
      ws.onerror = (error) => {
        console.error(`WebSocket连接错误 [${key}]:`, error)
        this.connectingStates.delete(key) // 清除连接中状态

        // 更新错误统计
        const quality = this.connectionQuality.get(key)
        if (quality) {
          quality.errorCount++
        }

        // 触发错误事件
        this.emit('error', { key, error, quality })

        if (options.onError) {
          options.onError(error, key)
        }
      }

      return ws
    } catch (error) {
      console.error(`WebSocket创建失败 [${key}]:`, error)
      this.connectingStates.delete(key) // 清除连接中状态
      return null
    }
  }

  /**
   * 启动心跳检测
   * @param {string} key - 连接标识
   * @param {number} interval - 心跳间隔(ms)
   */
  startHeartbeat(key, interval = this.defaultHeartbeatInterval) {
    this.stopHeartbeat(key) // 先停止现有心跳

    this.heartbeatIntervals.set(key, interval)
    this.lastHeartbeatTime.set(key, Date.now())

    const heartbeatTimer = setInterval(() => {
      if (this.isDestroyed) {
        this.stopHeartbeat(key)
        return
      }

      const ws = this.connections.get(key)
      if (!ws || ws.readyState !== WebSocket.OPEN) {
        this.stopHeartbeat(key)
        return
      }

      // 检查上次心跳时间，如果超时则认为连接异常
      const lastTime = this.lastHeartbeatTime.get(key) || 0
      const now = Date.now()

      if (now - lastTime > interval + this.heartbeatTimeout) {
        console.warn(`心跳超时 [${key}], 关闭连接`)
        this.stopHeartbeat(key)
        ws.close(1000, '心跳超时')
        return
      }

      // 发送心跳
      try {
        const heartbeatMsg = { type: 'heartbeat', timestamp: now }
        ws.send(JSON.stringify(heartbeatMsg))
        console.log(`发送心跳 [${key}]`)
        this.emit('heartbeat', { key, type: 'send', timestamp: now })
      } catch (error) {
        console.error(`发送心跳失败 [${key}]:`, error)
        this.stopHeartbeat(key)
      }
    }, interval)

    this.heartbeatTimers.set(key, heartbeatTimer)
    console.log(`心跳检测已启动 [${key}], 间隔: ${interval}ms`)
  }

  /**
   * 停止心跳检测
   * @param {string} key - 连接标识
   */
  stopHeartbeat(key) {
    const timer = this.heartbeatTimers.get(key)
    if (timer) {
      clearInterval(timer)
      this.heartbeatTimers.delete(key)
      this.heartbeatIntervals.delete(key)
      this.lastHeartbeatTime.delete(key)
      console.log(`心跳检测已停止 [${key}]`)
    }
  }

  /**
   * 断开WebSocket连接
   * @param {string} key - 连接标识
   * @param {boolean} preserveHandlers - 是否保留消息处理器
   */
  disconnect(key, preserveHandlers = false) {
    const ws = this.connections.get(key)
    if (ws) {
      // 停止心跳检测
      this.stopHeartbeat(key)

      ws.close(1000, '正常关闭')
      this.connections.delete(key)

      // 只有在不需要保留处理器时才清除
      if (!preserveHandlers) {
        this.messageHandlers.delete(key)
      }

      this.reconnectAttempts.delete(key)
      this.connectionConfigs.delete(key)
      this.connectionQuality.delete(key)
      // 清理重连定时器
      this.clearReconnectTimer(key)
      console.log(`WebSocket连接已断开: ${key}`)
    }
  }

  /**
   * 发送消息
   * @param {string} key - 连接标识
   * @param {Object|string} message - 要发送的消息
   */
  send(key, message) {
    const ws = this.connections.get(key)
    if (ws && ws.readyState === WebSocket.OPEN) {
      const data = typeof message === 'string' ? message : JSON.stringify(message)
      ws.send(data)
      console.log(`WebSocket发送消息 [${key}]:`, data)
      return true
    } else {
      console.warn(`WebSocket连接不可用 [${key}], 状态:`, ws ? ws.readyState : '连接不存在')
      return false
    }
  }

  /**
   * 注册消息处理器
   * @param {string} key - 连接标识
   * @param {Function} handler - 消息处理函数
   */
  onMessage(key, handler) {
    if (!this.messageHandlers.has(key)) {
      this.messageHandlers.set(key, [])
    }
    this.messageHandlers.get(key).push(handler)
    console.log(`已注册消息处理器 [${key}], 总数:`, this.messageHandlers.get(key).length)
  }

  /**
   * 移除消息处理器
   * @param {string} key - 连接标识
   * @param {Function} handler - 要移除的处理函数（可选）
   */
  removeMessageHandler(key, handler = null) {
    if (this.messageHandlers.has(key)) {
      if (handler) {
        const handlers = this.messageHandlers.get(key)
        const index = handlers.indexOf(handler)
        if (index > -1) {
          handlers.splice(index, 1)
        }
      } else {
        this.messageHandlers.delete(key)
      }
    }
  }

  /**
   * 处理接收到的消息
   * @param {string} key - 连接标识
   * @param {Object} data - 消息数据
   */
  handleMessage(key, data) {
    const handlers = this.messageHandlers.get(key)
    if (handlers && handlers.length > 0) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`消息处理器执行错误 [${key}]:`, error)
        }
      })
    } else {
      console.log(`没有注册的消息处理器 [${key}]`)
    }
  }

  /**
   * 尝试重连
   * @param {string} key - 连接标识
   * @param {string} url - 连接地址
   * @param {Object} options - 配置选项
   */
  attemptReconnect(key, url, options) {
    if (this.isDestroyed) {
      return
    }

    const attempts = this.reconnectAttempts.get(key) || 0

    // 无限重连模式（maxReconnectAttempts为0）或未达到最大重连次数
    if (this.maxReconnectAttempts === 0 || attempts < this.maxReconnectAttempts) {
      this.reconnectAttempts.set(key, attempts + 1)

      // 计算重连间隔，使用指数退避算法，但不超过最大间隔
      const backoffInterval = Math.min(
        this.reconnectInterval * Math.pow(1.5, Math.min(attempts, 10)), // 最多10次后保持最大间隔
        this.maxReconnectInterval
      )

      const maxAttemptsText = this.maxReconnectAttempts === 0 ? '∞' : this.maxReconnectAttempts
      console.log(`WebSocket重连尝试 [${key}]: ${attempts + 1}/${maxAttemptsText}, 延迟: ${backoffInterval}ms`)

      // 触发重连事件
      this.emit('reconnect', {
        key,
        attempt: attempts + 1,
        maxAttempts: this.maxReconnectAttempts,
        delay: backoffInterval,
        url
      })

      // 清理之前的重连定时器
      this.clearReconnectTimer(key)

      const timer = setTimeout(() => {
        if (!this.isDestroyed && (!this.connections.has(key) || this.connections.get(key).readyState === WebSocket.CLOSED)) {
          console.log(`开始重连 [${key}]...`)
          this.connect(key, url, options)
        }
        this.reconnectTimers.delete(key)
      }, backoffInterval)

      this.reconnectTimers.set(key, timer)
    } else {
      console.error(`WebSocket重连失败，已达到最大重连次数 [${key}]: ${this.maxReconnectAttempts}`)

      // 触发重连失败事件
      this.emit('reconnect', {
        key,
        attempt: attempts + 1,
        maxAttempts: this.maxReconnectAttempts,
        failed: true,
        url
      })
    }
  }

  /**
   * 清理重连定时器
   * @param {string} key - 连接标识
   */
  clearReconnectTimer(key) {
    const timer = this.reconnectTimers.get(key)
    if (timer) {
      clearTimeout(timer)
      this.reconnectTimers.delete(key)
      console.log(`已清理重连定时器 [${key}]`)
    }
  }

  /**
   * 获取连接状态
   * @param {string} key - 连接标识
   * @returns {number} WebSocket状态
   */
  getState(key) {
    const ws = this.connections.get(key)
    return ws ? ws.readyState : WebSocket.CLOSED
  }

  /**
   * 检查连接是否打开
   * @param {string} key - 连接标识
   * @returns {boolean}
   */
  isConnected(key) {
    return this.getState(key) === WebSocket.OPEN
  }

  /**
   * 获取连接状态文本
   * @param {string} key - 连接标识
   * @returns {string}
   */
  getStateText(key) {
    const state = this.getState(key)
    const stateMap = {
      [WebSocket.CONNECTING]: '连接中',
      [WebSocket.OPEN]: '已连接',
      [WebSocket.CLOSING]: '关闭中',
      [WebSocket.CLOSED]: '已关闭'
    }
    return stateMap[state] || '未知状态'
  }

  /**
   * 获取所有连接的状态
   * @returns {Object}
   */
  getAllStates() {
    const states = {}
    this.connections.forEach((ws, key) => {
      const quality = this.connectionQuality.get(key)
      states[key] = {
        state: ws.readyState,
        stateText: this.getStateText(key),
        isConnected: this.isConnected(key),
        quality: quality ? {
          ...quality,
          uptime: quality.connectTime ? Date.now() - quality.connectTime : 0,
          lastMessageAge: quality.lastMessageTime ? Date.now() - quality.lastMessageTime : 0
        } : null
      }
    })
    return states
  }

  /**
   * 获取连接质量信息
   * @param {string} key - 连接标识
   * @returns {Object|null}
   */
  getConnectionQuality(key) {
    const quality = this.connectionQuality.get(key)
    if (!quality) return null

    return {
      ...quality,
      uptime: quality.connectTime ? Date.now() - quality.connectTime : 0,
      lastMessageAge: quality.lastMessageTime ? Date.now() - quality.lastMessageTime : 0,
      isHealthy: this.isConnectionHealthy(key)
    }
  }

  /**
   * 检查连接是否健康
   * @param {string} key - 连接标识
   * @returns {boolean}
   */
  isConnectionHealthy(key) {
    if (!this.isConnected(key)) return false

    const quality = this.connectionQuality.get(key)
    if (!quality) return false

    const now = Date.now()
    const lastMessageAge = now - quality.lastMessageTime
    const heartbeatInterval = this.heartbeatIntervals.get(key) || this.defaultHeartbeatInterval

    // 如果超过2个心跳间隔没有收到消息，认为不健康
    return lastMessageAge < heartbeatInterval * 2
  }

  /**
   * 手动重连指定连接
   * @param {string} key - 连接标识
   */
  reconnect(key) {
    const config = this.connectionConfigs.get(key)
    if (config) {
      console.log(`手动重连 [${key}]...`)
      // 先清理重连定时器，避免与自动重连冲突
      this.clearReconnectTimer(key)
      // 重置重连次数
      this.reconnectAttempts.set(key, 0)
      // 断开现有连接
      const ws = this.connections.get(key)
      if (ws && ws.readyState !== WebSocket.CLOSED) {
        ws.close(1000, '手动重连')
      }
      this.connections.delete(key)
      // 立即重新连接
      this.connect(key, config.url, config.options)
    } else {
      console.warn(`无法重连 [${key}]: 未找到连接配置`)
    }
  }

  /**
   * 手动重连所有连接
   */
  reconnectAll() {
    console.log('手动重连所有WebSocket连接...')
    // 先获取所有配置的副本，避免在遍历过程中修改原集合
    const configs = new Map(this.connectionConfigs)
    configs.forEach((_, key) => {
      this.reconnect(key)
    })
  }

  /**
   * 断开所有连接
   */
  disconnectAll() {
    console.log('正在断开所有WebSocket连接...')
    // 清理所有重连定时器
    this.reconnectTimers.forEach((_, key) => {
      this.clearReconnectTimer(key)
    })
    this.connections.forEach((_, key) => {
      this.disconnect(key)
    })
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.isDestroyed = true

    // 停止所有心跳检测
    this.heartbeatTimers.forEach((_, key) => {
      this.stopHeartbeat(key)
    })

    this.disconnectAll()
    this.messageHandlers.clear()
    this.reconnectAttempts.clear()
    this.reconnectTimers.clear()
    this.connectionConfigs.clear()
    this.connectingStates.clear()
    this.heartbeatTimers.clear()
    this.heartbeatIntervals.clear()
    this.lastHeartbeatTime.clear()
    this.connectionQuality.clear()
    this.eventListeners.clear()
    console.log('WebSocket管理器已销毁')
  }
}

// 创建全局实例
const wsManager = new WebSocketManager()

// 确保页面刷新时正确清理连接
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    wsManager.destroy()
  })
}

export default wsManager 
