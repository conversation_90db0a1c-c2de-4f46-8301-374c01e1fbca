<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
      <el-form-item label="名称" prop="title">
        <el-input v-model="queryParams.title" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="类型名称" prop="categoryId">
        <el-select v-model="queryParams.categoryId" placeholder="请选择类型名称" clearable>
          <el-option v-for="dict in dict.type.medic_category" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['medicine:medicine:add']">新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['medicine:medicine:edit']">修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['medicine:medicine:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['medicine:medicine:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="medicineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="名称" align="center" prop="title" min-width="120" />
      <!-- <el-table-column label="含量" align="center" prop="titleContent" min-width="100" /> -->
      <el-table-column label="类型名称" align="center" prop="categoryName" min-width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.medic_category" :value="scope.row.categoryName" />
        </template>
      </el-table-column>
      <el-table-column label="厂家" align="center" prop="factory" min-width="120" />
      <el-table-column label="卡编号" align="center" prop="cardCode" min-width="100" />
      <el-table-column label="入库时重量" align="center" prop="weightFull" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.weightFull }}kg</span>
        </template>
      </el-table-column>
      <el-table-column label="当前重量" align="center" prop="weightNow" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.weightNow }}kg</span>
        </template>
      </el-table-column>
      <el-table-column label="包装重量" align="center" prop="weightLow" min-width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.weightLow }}kg</span>
        </template>
      </el-table-column>
      <el-table-column label="入库时间" align="center" prop="timeIn" min-width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.timeIn, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入库人" align="center" prop="performerName" min-width="100" />
      <el-table-column label="借还状态" align="center" prop="borrowStatus" min-width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.borrowStatus === 1 ? 'success' : 'danger'">
            {{ scope.row.borrowStatus === 1 ? '未借出' : '已借出' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['medicine:medicine:edit']">修改</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['medicine:medicine:remove']">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-close" @click="handleCancelCard(scope.row)"
            v-hasPermi="['medicine:medicine:cancelcard']">销卡</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改样品信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="名称" prop="title">
              <el-input v-model="form.title" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="简称" prop="titleName">
              <el-input v-model="form.title" placeholder="请输入简称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="含量" prop="titleContent">
              <el-input v-model="form.titleContent" placeholder="请输入含量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="厂家" prop="factory">
              <el-input v-model="form.factory" placeholder="请输入厂家" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="卡编号" prop="cardCode">
              <el-input v-model="form.cardCode" placeholder="请输入卡编号" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库时重量" prop="weightFull">
              <el-input v-model="form.weightFull" placeholder="请输入重量">
                <template slot="append">kg</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型名称" prop="categoryId">
              <el-select v-model="form.categoryId" placeholder="请选择类型名称" style="width: 100%"
                @change="handleCategoryChange">
                <el-option v-for="dict in dict.type.medic_category" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="当前重量" prop="weightNow">
              <el-input v-model="form.weightNow" placeholder="请输入重量">
                <template slot="append">kg</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="包装重量" prop="weightLow">
              <el-input v-model="form.weightLow" placeholder="请输入重量">
                <template slot="append">kg</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="入库时间" prop="timeIn">
              <el-date-picker v-model="form.timeIn" type="date" value-format="yyyy-MM-dd" placeholder="请选择入库时间"
                style="width: 100%">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库人" prop="performerId">
              <el-select v-model="form.performerId" placeholder="请选择入库人" style="width: 100%"
                @change="handlePerformerChange">
                <el-option v-for="user in userOptions" :key="user.userId" :label="user.nickName"
                  :value="user.userId"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-form-item label="借还状态" prop="borrowStatus">
          <el-radio-group v-model="form.borrowStatus">
            <el-radio :label="0">未借出</el-radio>
            <el-radio :label="1">已借出</el-radio>
          </el-radio-group>
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMedicine, getMedicine, delMedicine, addMedicine, updateMedicine, removeMedicineCard } from "@/api/medicine/medicine"
import { listUser } from "@/api/system/user"
import wsManager from "@/utils/websocket"

export default {
  name: "Medicine",
  dicts: ['medic_category'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 样品信息表格数据
      medicineList: [],
      // 用户选项
      userOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        categoryId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        titleName: [
          { required: true, message: "简称不能为空", trigger: "blur" }
        ],
        titleContent: [
          { required: true, message: "含量不能为空", trigger: "blur" }
        ],
        categoryId: [
          { required: true, message: "类型名称不能为空", trigger: "change" }
        ],
        factory: [
          { required: true, message: "厂家不能为空", trigger: "blur" }
        ],
        cardCode: [
          { required: true, message: "卡编号不能为空", trigger: "blur" }
        ],
        weightFull: [
          { required: true, message: "入库时重量不能为空", trigger: "blur" },
          { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的重量数值", trigger: "blur" }
        ],
        weightNow: [
          { required: true, message: "当前重量不能为空", trigger: "blur" },
          { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的重量数值", trigger: "blur" }
        ],
        weightLow: [
          { required: true, message: "包装重量不能为空", trigger: "blur" },
          { pattern: /^\d+(\.\d+)?$/, message: "请输入有效的重量数值", trigger: "blur" }
        ],
        timeIn: [
          { required: true, message: "入库时间不能为空", trigger: "change" }
        ],
        performerId: [
          { required: true, message: "入库人不能为空", trigger: "change" }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getUserOptions()
    this.initWebSocket()
  },
  beforeDestroy() {
    // 移除WebSocket消息处理器
    wsManager.removeMessageHandler('main', this.handleWebSocketMessage)
    console.log('样品信息页面已移除WebSocket消息处理器')
  },
  methods: {
    /** 查询样品信息列表 */
    getList() {
      this.loading = true
      listMedicine(this.queryParams).then(response => {
        this.medicineList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 获取用户选项 */
    getUserOptions() {
      listUser({ pageSize: 999 }).then(response => {
        this.userOptions = response
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        medicineId: null,
        title: null,
        titleContent: null,
        categoryId: null,
        categoryName: null,
        factory: null,
        cardCode: null,
        weightFull: null,
        weightNow: null,
        weightLow: null,
        timeIn: null,
        performerId: null,
        performerName: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.medicineId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加样品信息"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const medicineId = row.medicineId || this.ids
      getMedicine(medicineId).then(response => {
        this.form = response.data

        // 如果后端返回的是categoryName，需要转换为categoryId
        if (this.form.categoryName && !this.form.categoryId) {
          // 根据categoryName找到对应的categoryId
          const matchDict = this.dict.type.medic_category.find(dict => dict.label === this.form.categoryName)
          if (matchDict) {
            this.form.categoryId = matchDict.value
          }
        }

        // 如果后端返回的是performerName，需要转换为performerId
        if (this.form.performerName && !this.form.performerId) {
          // 根据performerName找到对应的performerId
          const matchUser = this.userOptions.find(user => user.nickName === this.form.performerName)
          if (matchUser) {
            this.form.performerId = matchUser.userId
          }
        }

        this.open = true
        this.title = "修改样品信息"
      })
    },

    /** 处理类型选择变化 */
    handleCategoryChange(categoryId) {
      if (categoryId) {
        // 根据categoryId找到对应的categoryName
        const matchDict = this.dict.type.medic_category.find(dict => dict.value === categoryId)
        if (matchDict) {
          this.form.categoryName = matchDict.label
        }
      } else {
        this.form.categoryName = null
      }
    },

    /** 处理入库人选择变化 */
    handlePerformerChange(performerId) {
      if (performerId) {
        // 根据performerId找到对应的performerName
        const matchUser = this.userOptions.find(user => user.userId === performerId)
        if (matchUser) {
          this.form.performerName = matchUser.nickName
        }
      } else {
        this.form.performerName = null
      }
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.medicineId != null) {
            updateMedicine(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addMedicine(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const medicineIds = row.medicineId || this.ids
      this.$modal.confirm('是否确认删除样品信息编号为"' + medicineIds + '"的数据项？').then(function () {
        return delMedicine(medicineIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('medicine/medicine/export', {
        ...this.queryParams
      }, `medicine_${new Date().getTime()}.xlsx`)
    },
    /** 销卡按钮操作 */
    handleCancelCard(row) {
      this.$modal.confirm('是否确认销卡样品"' + row.title + '"？').then(() => {
        return removeMedicineCard(row)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("销卡成功")
      }).catch(() => { })
    },

    // 初始化WebSocket连接
    initWebSocket() {
      // 注册样品信息页面的消息处理器
      wsManager.onMessage('main', this.handleWebSocketMessage)
      // 注册称重传感器消息处理器
      wsManager.onMessage('weight', this.handleWeightMessage)
      console.log('样品信息页面已注册WebSocket消息处理器')
    },

    // 处理WebSocket消息
    handleWebSocketMessage(data) {
      console.log('样品信息页面收到WebSocket消息:', data)

      // 如果表单是打开状态就自动填入卡号
      if (this.open) {
        let cardCode = null

        // 处理新的WebSocket返回格式
        // {
        //   "cardCode": "E200421C507060130537226F",
        //   "cardType": 1,
        //   "connId": 1072,
        //   "flag": "E200",
        //   "id": 1423,
        //   "params": {}
        // }
        if (data.cardCode && data.cardType === 1) {
          cardCode = data.cardCode.trim()
          console.log('识别到样品卡，cardType为1:', cardCode, 'cardType:', data.cardType)
        } else if (data.cardCode && data.cardType === 0) {
          this.$message.error('cardType为0')
          return
        } else {
          this.$message.error('cardType为null')
          return
        }

        if (cardCode) {
          // 覆盖表单中的卡编号字段
          this.$set(this.form, 'cardCode', cardCode)
          this.$message.success('已自动填入卡编号: ' + cardCode)
          console.log('已更新卡编号字段:', cardCode)
        } else {
          if (data.cardCode && data.cardType !== undefined && data.cardType !== null && data.cardType !== 1) {
            console.log('收到卡号但cardType不为1，不处理:', data.cardCode, 'cardType:', data.cardType)
          } else {
            console.log('未识别到有效样品卡号，数据格式:', typeof data, data)
          }
        }
      } else {
        console.log('表单未打开，不处理卡号消息')
      }
    },

    /** 处理称重消息 */
    handleWeightMessage(data) {
      console.log('主页收到称重消息:', data, '数据类型:', typeof data)
      if (!this.form.medicineId) {
        this.form.weightFull = data.toString()
        this.form.weightNow = data.toString()
        this.$message.success(`已自动填入重量: ${data}kg`)
        console.log('✓ 成功填入重量:', data)
      }
    },
  }
}
</script>
