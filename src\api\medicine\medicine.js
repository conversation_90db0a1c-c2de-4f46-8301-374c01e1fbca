import request from '@/utils/request'

// 查询样品信息列表
export function listMedicine(query) {
  return request({
    url: '/medicine/medicine/list',
    method: 'get',
    params: query
  })
}

// 查询样品信息详细
export function getMedicine(medicineId) {
  return request({
    url: '/medicine/medicine/' + medicineId,
    method: 'get'
  })
}

// 新增样品信息
export function addMedicine(data) {
  return request({
    url: '/medicine/medicine',
    method: 'post',
    data: data
  })
}

// 修改样品信息
export function updateMedicine(data) {
  return request({
    url: '/medicine/medicine',
    method: 'put',
    data: data
  })
}

// 删除样品信息
export function delMedicine(medicineId) {
  return request({
    url: '/medicine/medicine/' + medicineId,
    method: 'delete'
  })
}

// 销卡操作
export function removeMedicineCard(data) {
  return request({
    url: '/medicine/medicine/removeMedicineCard',
    method: 'delete',
    data: data
  })
}
