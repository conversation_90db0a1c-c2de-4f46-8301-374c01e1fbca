<template>
  <div class="app-container">
    <!-- 操作面板 -->
    <el-card class="operation-panel" shadow="hover">
      <!-- WebSocket连接状态 -->
      <div class="connection-status-bar">
        <div class="status-item">
          <i class="el-icon-link" style="margin-right: 4px"></i>
          <span>连接状态：</span>
          <el-tag :type="getConnectionType('main')" size="small" style="margin-left: 4px" class="connection-tag">
            刷卡器: {{ getConnectionStatusText("main") }}
          </el-tag>
          <el-tag :type="getConnectionType('weight')" size="small" style="margin-left: 8px" class="connection-tag">
            称重传感器: {{ getConnectionStatusText("weight") }}
          </el-tag>
        </div>
        <div class="status-actions">
          <el-button size="mini" type="text" @click="refreshConnections" :loading="refreshing">
            <i class="el-icon-refresh"></i> 刷新连接
          </el-button>
        </div>
      </div>

      <el-row>
        <el-col :span="6">
          <div style="margin: 0 0 10px 0">
            <el-input v-model="queryParamsForReconize.name" placeholder="请输入工号搜索用户卡号" clearable
              @keyup.enter.native="queryUserCard" />
          </div>
        </el-col>
        <el-col :span="2">
          <div style="margin: 4px 0 0 10px">
            <el-button type="primary" icon="el-icon-search" size="mini" @click="queryUserCard">搜索</el-button>
          </div>
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="reconizeTable" border>
        <el-table-column label="用户编号" align="center" prop="userId" width="250" />
        <el-table-column label="操作人" align="center" prop="userName" width="250" />
        <el-table-column label="样品卡号" align="center" prop="medicineId" width="250" />
        <el-table-column label="含量及名称" align="center" prop="medicineName" />
        <el-table-column label="借还操作" align="center" prop="borrow" width="250">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.borrow !== ''" :type="getBorrowType(scope.row.borrow) === 'borrow'
                ? 'danger'
                : 'success'
              ">
              {{ getBorrowText(scope.row.borrow) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="样品重量" align="center" prop="weight" width="250" />
      </el-table>
    </el-card>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="recordList" style="margin-top: 20px">
      <el-table-column label="用户名" align="center" prop="userName" width="200" />
      <el-table-column label="操作时间" align="center" prop="createTime" width="200" />
      <el-table-column label="样品名称" align="center" prop="medicineName" />
      <!-- weightNow -->
      <el-table-column label="样品重量" align="center" prop="weightNow" />
      <el-table-column label="借还类型" align="center" prop="borrow" width="200">
        <template slot-scope="scope">
          <el-tag :type="getBorrowType(scope.row.borrow) === 'borrow'
              ? 'danger'
              : 'success'
            ">
            {{ getBorrowText(scope.row.borrow) }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { listRecord, addRecord } from "@/api/medicine/record"
import { getUserByCardCode, getUserByUserName } from "@/api/system/user"
import { getMedicineByCardCode } from "@/api/medicine/medicine"
import wsManager from "@/utils/websocket"

export default {
  name: "Index",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 借还记录表格数据
      recordList: [],
      // 识别数据表格
      reconizeTable: [{
        userId: '',           // 用户编号
        userName: '',         // 操作人
        medicineId: '',       // 样品卡号
        medicineName: '',     // 含量及名称
        borrow: '',           // 操作类型
        weight: ''            // 样品重量
      }],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        medicineId: null,
        userName: null,
        userDept: null,
        userCard: null,
        medicineName: null,
        medicineCard: null,
        medicineNameNor: null,
        categoryId: null,
        categoryName: null,
        borrow: null,
        borrowLoss: null,
        borrowId: null,
        weightBefore: null,
        weightNow: null,
        weightBorrow: null,
        weightLoss: null,
        weightIn: null,
        weightLow: null
      },
      // 查询参数2
      queryParamsForReconize: {
        name: null
      },
      // 操作表单
      // operationForm: {
      //   userId: '',
      //   medicineId: '',
      //   weight: '',
      //   autoSubmit: true
      // },
      // 连接刷新状态
      refreshing: false
    }
  },
  computed: {
    // 检查刷卡器连接状态
    isCardConnected() {
      return wsManager.isConnected('main')
    },
    // 检查称重传感器连接状态
    isWeightConnected() {
      return wsManager.isConnected('weight')
    }
  },
  created() {
    this.getList()
    this.initWebSocketHandlers()
    // 初始化识别表格数据
    this.resetRecognizeTable()
    // 定时检查连接状态
    this.statusInterval = setInterval(() => {
      this.$forceUpdate() // 强制更新计算属性
    }, 2000)
  },
  beforeDestroy() {
    // 清理定时器
    if (this.statusInterval) {
      clearInterval(this.statusInterval)
    }
    // 清理WebSocket消息处理器
    wsManager.removeMessageHandler('main', this.handleCardMessage)
    wsManager.removeMessageHandler('weight', this.handleWeightMessage)

    console.log('主页已清理WebSocket消息处理器')
  },
  methods: {
    /** 查询借还记录列表 */
    getList() {
      this.loading = true
      listRecord(this.queryParams).then(response => {
        this.recordList = response.rows
        this.total = response.total
        this.loading = false
      })
    },

    /** 初始化WebSocket消息处理器 */
    initWebSocketHandlers() {
      // wsManager.removeMessageHandler('main')
      // wsManager.removeMessageHandler('weight')
      // 注册刷卡器消息处理器
      wsManager.onMessage('main', this.handleCardMessage)
      // 注册称重传感器消息处理器
      wsManager.onMessage('weight', this.handleWeightMessage)

      console.log('主页已注册WebSocket消息处理器')
    },

    /** 处理刷卡消息 */
    handleCardMessage(data) {
      console.log(this.$route.path)
      if (this.$route.path != '/index') {
        return
      }
      console.log('主页收到刷卡消息:', data)
      if (data == null) {
        return
      }
      // 根据WebSocket返回格式处理
      if (data.cardCode) {
        if (data.cardType === 0) {
          // cardType为0时填入用户编号并获取用户信息
          this.handleUserCard(data.cardCode)
        } else if (data.cardType === 1) {
          // cardType为1时填入药品编号并获取药品信息
          this.handleMedicineCard(data.cardCode)
        } else {
          this.$message.error(`该卡未绑定用户或样品`)
        }
      }
    },

    /** 处理用户卡 */
    async handleUserCard(cardCode) {
      try {
        // 填入用户编号
        this.$set(this.reconizeTable[0], 'userId', cardCode)
        this.$message.success(`已填入用户编号: ${cardCode}`)
        console.log('填入用户编号:', cardCode)

        // 调用接口获取用户信息
        const response = await getUserByCardCode(cardCode)
        if (response) {
          // 填入操作人信息
          this.$set(this.reconizeTable[0], 'userName', response.nickName || response.userName)
          this.$message.success(`已获取用户信息: ${response.nickName || response.userName}`)
          console.log('获取用户信息成功:', response)
        }
      } catch (error) {
        console.error('获取用户信息失败:', error)
        this.$message.error('获取用户信息失败，请检查卡号是否正确')
      }

      // 检查是否可以自动提交
      this.checkAutoSubmit()
    },

    /** 处理药品卡 */
    async handleMedicineCard(cardCode) {
      try {
        // 填入样品卡号
        this.$set(this.reconizeTable[0], 'medicineId', cardCode)
        this.$message.success(`已填入样品卡号: ${cardCode}`)
        console.log('填入样品卡号:', cardCode)

        // 调用接口获取药品信息
        const response = await getMedicineByCardCode(cardCode)
        if (response) {
          // 填入含量及名称
          const medicineName = `${response.title || ''}`.trim()
          this.$set(this.reconizeTable[0], 'medicineName', medicineName)

          // 根据borrow字段填入操作类型
          if (response.borrowStatus !== undefined && response.borrowStatus !== null) {
            this.$set(this.reconizeTable[0], 'borrow', response.borrowStatus == 1 ? '0' : '1')
          }

          this.$message.success(`已获取药品信息: ${medicineName}`)
          console.log('获取药品信息成功:', response)
        }
      } catch (error) {
        console.error('获取药品信息失败:', error)
        this.$message.error('获取药品信息失败，请检查卡号是否正确')
      }

      // 检查是否可以自动提交
      this.checkAutoSubmit()
    },

    /** 处理称重消息 */
    handleWeightMessage(data) {
      console.log(this.$route.path)
      if (this.$route.path != '/index') {
        return
      }
      console.log('主页收到称重消息:', data, '数据类型:', typeof data)
      if (data == null || data.text == 'null') {
        return
      }
      // 填入样品重量
      this.$set(this.reconizeTable[0], 'weight', data.toString())
      this.$message.success(`已自动填入重量: ${data}kg`)
      console.log('✓ 成功填入重量:', data)

      // 检查是否可以自动提交
      this.checkAutoSubmit()
    },

    /** 获取借还类型 */
    getBorrowType(borrow) {
      // 统一转换为字符串进行比较
      const borrowStr = String(borrow)
      return borrowStr === '0' ? 'borrow' : 'return'
    },

    /** 获取借还类型文本 */
    getBorrowText(borrow) {
      // 统一转换为字符串进行比较
      const borrowStr = String(borrow)
      if (borrowStr === '0') {
        return '借出'
      } else if (borrowStr === '1') {
        return '归还'
      } else {
        return '未知'
      }
    },

    /** 获取连接状态文本 */
    getConnectionStatusText(key) {
      const state = wsManager.getState(key)
      const stateMap = {
        [WebSocket.CONNECTING]: '连接中',
        [WebSocket.OPEN]: '已连接',
        [WebSocket.CLOSING]: '断开中',
        [WebSocket.CLOSED]: '已断开'
      }
      return stateMap[state] || '未知'
    },

    /** 获取连接状态类型（用于标签颜色） */
    getConnectionType(key) {
      const state = wsManager.getState(key)
      // 只有完全连接成功(OPEN)才显示绿色，其他状态都显示红色
      return state === WebSocket.OPEN ? 'success' : 'danger'
    },

    /** 刷新WebSocket连接 */
    refreshConnections() {
      this.refreshing = true
      this.$message.info('正在重新建立连接...')

      try {
        // 调用全局重连方法
        this.$store.dispatch('user/reconnectWebSocket')

        // 重新注册消息处理器
        setTimeout(() => {
          wsManager.onMessage('main', this.handleCardMessage)
          wsManager.onMessage('weight', this.handleWeightMessage)
        }, 100)

        // 延迟2秒后检查连接状态
        setTimeout(() => {
          this.refreshing = false
          const cardConnected = this.isCardConnected
          const weightConnected = this.isWeightConnected

          if (cardConnected && weightConnected) {
            this.$message.success('所有连接已重新建立成功')
          } else if (cardConnected || weightConnected) {
            this.$message.warning('部分连接重新建立成功，请检查网络状态')
          } else {
            this.$message.error('连接重新建立失败，请检查网络和服务器状态')
          }
        }, 2000)

      } catch (error) {
        this.refreshing = false
        this.$message.error('重新建立连接时发生错误: ' + error.message)
        console.error('刷新连接错误:', error)
      }
    },



    /** 重新注册WebSocket消息处理器 */
    reregisterWebSocketHandlers() {
      // 重新注册刷卡器消息处理器
      wsManager.onMessage('main', this.handleCardMessage)
      // 重新注册称重传感器消息处理器
      wsManager.onMessage('weight', this.handleWeightMessage)
      console.log('主页已重新注册WebSocket消息处理器')
    },

    /** 检查是否可以自动提交记录 */
    checkAutoSubmit() {
      const record = this.reconizeTable[0]

      // 检查所有必要字段是否都已填充
      if (record.userId && record.medicineId && record.weight) {
        console.log('所有必要数据已填充完毕，准备自动提交:', record)
        this.autoSubmitRecord()
      } else {
        console.log('数据未完整，等待更多数据:', {
          userId: record.userId,
          medicineId: record.medicineId,
          weight: record.weight
        })
      }
    },

    /** 自动提交记录 */
    async autoSubmitRecord() {
      try {
        const record = this.reconizeTable[0]

        // 准备提交数据
        const submitData = {
          userCard: record.userId,
          medicineCard: record.medicineId,
          weightNow: parseFloat(record.weight)
        }

        console.log('正在自动提交记录:', submitData)
        this.$message.info('正在自动提交借还记录...')

        // 调用添加记录接口
        await addRecord(submitData)

        this.$message.success('借还记录已自动提交成功！')
        console.log('记录提交成功')

        // 刷新记录列表
        this.getList()

        // 清空识别表格数据，准备下一次操作
        this.resetRecognizeTable()

      } catch (error) {
        // 清空识别表格数据，准备下一次操作
        this.resetRecognizeTable()
        console.error('自动提交记录失败:', error)
        this.$message.error('自动提交记录失败: ' + (error.message || '未知错误'))
      }
    },

    /** 重置识别表格数据 */
    resetRecognizeTable() {
      this.$set(this.reconizeTable, 0, {
        userId: '',
        userName: '',
        medicineId: '',
        medicineName: '',
        borrow: '',
        weight: ''
      })
      console.log('识别表格数据已重置')
    },

    /** 根据用户工号搜索用户卡号 */
    queryUserCard() {
      getUserByUserName(this.queryParamsForReconize.name).then(response => {
        console.log(response)
        if (response.cardCode) {
          this.reconizeTable[0].userId = response.cardCode
          this.reconizeTable[0].userName = response.nickName
        } else {
          this.$message.error('该用户未绑定卡号')
        }
      })
    }
  }
}
</script>

<style scoped>
.connection-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.status-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.status-item i {
  color: #909399;
}

.status-actions {
  display: flex;
  align-items: center;
}

.operation-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
}

/* 连接状态标签动画效果 */
.el-tag {
  transition: all 0.3s ease;
}

/* 连接状态标签样式 */
.connection-tag.el-tag--success {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: #ffffff !important;
  animation: pulse-success 2s infinite;
}

.el-tag.el-tag--success {
  animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(103, 194, 58, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

.el-tag.el-tag--danger {
  animation: pulse-danger 1.5s infinite;
}

@keyframes pulse-danger {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.4);
  }

  70% {
    box-shadow: 0 0 0 6px rgba(245, 108, 108, 0);
  }

  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}
</style>
