<template>
    <div class="app-container">
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
                    v-hasPermi="['medicine:record:export']">导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="recordList">
            <el-table-column label="记录ID" align="center" prop="id" />
            <el-table-column label="用户ID" align="center" prop="userId" />
            <el-table-column label="用户名" align="center" prop="userName" />
            <el-table-column label="用户部门" align="center" prop="userDept" />
            <el-table-column label="用户绑定卡号" align="center" prop="userCard" />
            <el-table-column label="样品ID" align="center" prop="medicineId" />
            <el-table-column label="样品名称" align="center" prop="medicineName" />
            <el-table-column label="样品绑定卡号" align="center" prop="medicineCard" />
            <el-table-column label="不含含量的名称" align="center" prop="medicineNameNor" />
            <el-table-column label="类型ID" align="center" prop="categoryId" />
            <el-table-column label="类型名称" align="center" prop="categoryName" />
            <el-table-column label="0借出,1归还" align="center" prop="borrow" />
            <el-table-column label="逾期天数" align="center" prop="borrowLoss" />
            <el-table-column label="借出时的id" align="center" prop="borrowId" />
            <el-table-column label="借前的重量,从medicine查" align="center" prop="weightBefore" />
            <el-table-column label="还时的重量" align="center" prop="weightNow" />
            <el-table-column label="归还后重量差" align="center" prop="weightBorrow" />
            <el-table-column label="借出时，重量与上一次重量不一致，记录值" align="center" prop="weightLoss" />
            <el-table-column label="入库时重量" align="center" prop="weightIn" />
            <el-table-column label="包装重量，且当低于该重量后自动取消关联" align="center" prop="weightLow" />
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
    </div>
</template>

<script>
import { listRecord, getRecord, delRecord, addRecord, updateRecord } from "@/api/medicine/record"

export default {
    name: "Record",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 借还记录表格数据
            recordList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 表单参数
            form: {},
            // 表单校验
            rules: {
            },
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 10,
                userId: null,
                userName: null,
                userDept: null,
                userCard: null,
                medicineId: null,
                medicineName: null,
                medicineCard: null,
                medicineNameNor: null,
                categoryId: null,
                categoryName: null,
                borrow: null,
                borrowLoss: null,
                borrowId: null,
                weightBefore: null,
                weightNow: null,
                weightBorrow: null,
                weightLoss: null,
                weightIn: null,
                weightLow: null
            },
        }
    },
    created() {

        // console.log(this.$route.params);
        
        // console.log(JSON.parse(JSON.stringify(this.$route.params)));
        
        this.getList()
    },
    methods: {
        /** 查询借还记录列表 */
        getList() {
            this.loading = true
            listRecord(this.queryParams).then(response => {
                this.recordList = response.rows
                this.total = response.total
                this.loading = false
            })
        },
        /** 导出按钮操作 */
        handleExport() {
            this.download('medicine/record/export', {
                ...this.queryParams
            }, `record_${new Date().getTime()}.xlsx`)
        }
    }
}
</script>
