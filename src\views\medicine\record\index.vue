<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <!-- <el-form-item label="用户ID" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="用户名" prop="userName">
        <el-input
          v-model="queryParams.userName"
          placeholder="请输入用户名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="用户部门" prop="userDept">
        <el-input
          v-model="queryParams.userDept"
          placeholder="请输入用户部门"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="用户绑定卡号" prop="userCard">
        <el-input
          v-model="queryParams.userCard"
          placeholder="请输入用户绑定卡号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="样品ID" prop="medicineId">
        <el-input
          v-model="queryParams.medicineId"
          placeholder="请输入样品ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="样品名称" prop="medicineName">
        <el-input
          v-model="queryParams.medicineName"
          placeholder="请输入样品名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="样品绑定卡号" prop="medicineCard">
        <el-input
          v-model="queryParams.medicineCard"
          placeholder="请输入样品绑定卡号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="不含含量的名称" prop="medicineNameNor">
        <el-input
          v-model="queryParams.medicineNameNor"
          placeholder="请输入不含含量的名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型ID" prop="categoryId">
        <el-input
          v-model="queryParams.categoryId"
          placeholder="请输入类型ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型名称" prop="categoryName">
        <el-input
          v-model="queryParams.categoryName"
          placeholder="请输入类型名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="0借出,1归还" prop="borrow">
        <el-input
          v-model="queryParams.borrow"
          placeholder="请输入0借出,1归还"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="逾期天数" prop="borrowLoss">
        <el-input
          v-model="queryParams.borrowLoss"
          placeholder="请输入逾期天数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="借出时的id" prop="borrowId">
        <el-input
          v-model="queryParams.borrowId"
          placeholder="请输入借出时的id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="借前的重量,从medicine查" prop="weightBefore">
        <el-input
          v-model="queryParams.weightBefore"
          placeholder="请输入借前的重量,从medicine查"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="还时的重量" prop="weightNow">
        <el-input
          v-model="queryParams.weightNow"
          placeholder="请输入还时的重量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="归还后重量差" prop="weightBorrow">
        <el-input
          v-model="queryParams.weightBorrow"
          placeholder="请输入归还后重量差"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="借出时，重量与上一次重量不一致，记录值" prop="weightLoss">
        <el-input
          v-model="queryParams.weightLoss"
          placeholder="请输入借出时，重量与上一次重量不一致，记录值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="入库时重量" prop="weightIn">
        <el-input
          v-model="queryParams.weightIn"
          placeholder="请输入入库时重量"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="包装重量，且当低于该重量后自动取消关联" prop="weightLow">
        <el-input
          v-model="queryParams.weightLow"
          placeholder="请输入包装重量，且当低于该重量后自动取消关联"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['medicine:record:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['medicine:record:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['medicine:record:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['medicine:record:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="recordList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="id" />
      <el-table-column label="用户ID" align="center" prop="userId" />
      <el-table-column label="用户名" align="center" prop="userName" />
      <el-table-column label="用户部门" align="center" prop="userDept" />
      <el-table-column label="用户绑定卡号" align="center" prop="userCard" />
      <el-table-column label="样品ID" align="center" prop="medicineId" />
      <el-table-column label="样品名称" align="center" prop="medicineName" />
      <el-table-column
        label="样品绑定卡号"
        align="center"
        prop="medicineCard"
      />
      <el-table-column
        label="不含含量的名称"
        align="center"
        prop="medicineNameNor"
      />
      <el-table-column label="类型ID" align="center" prop="categoryId" />
      <el-table-column label="类型名称" align="center" prop="categoryName" />
      <el-table-column label="0借出,1归还" align="center" prop="borrow" />
      <el-table-column label="逾期天数" align="center" prop="borrowLoss" />
      <el-table-column label="借出时的id" align="center" prop="borrowId" />
      <el-table-column label="借前的重量" align="center" prop="weightBefore" />
      <el-table-column label="还时的重量" align="center" prop="weightNow" />
      <el-table-column
        label="归还后重量差"
        align="center"
        prop="weightBorrow"
      />
      <el-table-column label="自然损耗重量" align="center" prop="weightLoss" />
      <el-table-column label="入库重量" align="center" prop="weightIn" />
      <el-table-column label="包装重量" align="center" prop="weightLow" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['medicine:record:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['medicine:record:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改借还记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="用户ID" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户ID" />
        </el-form-item>
        <el-form-item label="用户名" prop="userName">
          <el-input v-model="form.userName" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="用户部门" prop="userDept">
          <el-input v-model="form.userDept" placeholder="请输入用户部门" />
        </el-form-item>
        <el-form-item label="用户绑定卡号" prop="userCard">
          <el-input v-model="form.userCard" placeholder="请输入用户绑定卡号" />
        </el-form-item>
        <el-form-item label="样品ID" prop="medicineId">
          <el-input v-model="form.medicineId" placeholder="请输入样品ID" />
        </el-form-item>
        <el-form-item label="样品名称" prop="medicineName">
          <el-input v-model="form.medicineName" placeholder="请输入样品名称" />
        </el-form-item>
        <el-form-item label="样品绑定卡号" prop="medicineCard">
          <el-input
            v-model="form.medicineCard"
            placeholder="请输入样品绑定卡号"
          />
        </el-form-item>
        <el-form-item label="不含含量的名称" prop="medicineNameNor">
          <el-input
            v-model="form.medicineNameNor"
            placeholder="请输入不含含量的名称"
          />
        </el-form-item>
        <el-form-item label="类型ID" prop="categoryId">
          <el-input v-model="form.categoryId" placeholder="请输入类型ID" />
        </el-form-item>
        <el-form-item label="类型名称" prop="categoryName">
          <el-input v-model="form.categoryName" placeholder="请输入类型名称" />
        </el-form-item>
        <el-form-item label="0借出,1归还" prop="borrow">
          <el-input v-model="form.borrow" placeholder="请输入0借出,1归还" />
        </el-form-item>
        <el-form-item label="逾期天数" prop="borrowLoss">
          <el-input v-model="form.borrowLoss" placeholder="请输入逾期天数" />
        </el-form-item>
        <el-form-item label="借出时的id" prop="borrowId">
          <el-input v-model="form.borrowId" placeholder="请输入借出时的id" />
        </el-form-item>
        <el-form-item label="借前的重量,从medicine查" prop="weightBefore">
          <el-input
            v-model="form.weightBefore"
            placeholder="请输入借前的重量,从medicine查"
          />
        </el-form-item>
        <el-form-item label="还时的重量" prop="weightNow">
          <el-input v-model="form.weightNow" placeholder="请输入还时的重量" />
        </el-form-item>
        <el-form-item label="归还后重量差" prop="weightBorrow">
          <el-input
            v-model="form.weightBorrow"
            placeholder="请输入归还后重量差"
          />
        </el-form-item>
        <el-form-item
          label="借出时，重量与上一次重量不一致，记录值"
          prop="weightLoss"
        >
          <el-input
            v-model="form.weightLoss"
            placeholder="请输入借出时，重量与上一次重量不一致，记录值"
          />
        </el-form-item>
        <el-form-item label="入库时重量" prop="weightIn">
          <el-input v-model="form.weightIn" placeholder="请输入入库时重量" />
        </el-form-item>
        <el-form-item
          label="包装重量，且当低于该重量后自动取消关联"
          prop="weightLow"
        >
          <el-input
            v-model="form.weightLow"
            placeholder="请输入包装重量，且当低于该重量后自动取消关联"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRecord, getRecord, delRecord, addRecord, updateRecord } from "@/api/medicine/record"

export default {
  name: "Record",
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 借还记录表格数据
      recordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: null,
        userName: null,
        userDept: null,
        userCard: null,
        medicineId: null,
        medicineName: null,
        medicineCard: null,
        medicineNameNor: null,
        categoryId: null,
        categoryName: null,
        borrow: null,
        borrowLoss: null,
        borrowId: null,
        weightBefore: null,
        weightNow: null,
        weightBorrow: null,
        weightLoss: null,
        weightIn: null,
        weightLow: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    }
  },
  created () {
    this.getList()
  },
  methods: {
    /** 查询借还记录列表 */
    getList () {
      this.loading = true
      listRecord(this.queryParams).then(response => {
        this.recordList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset () {
      this.form = {
        id: null,
        userId: null,
        userName: null,
        userDept: null,
        userCard: null,
        medicineId: null,
        medicineName: null,
        medicineCard: null,
        medicineNameNor: null,
        categoryId: null,
        categoryName: null,
        borrow: null,
        borrowLoss: null,
        borrowId: null,
        weightBefore: null,
        weightNow: null,
        weightBorrow: null,
        weightLoss: null,
        weightIn: null,
        weightLow: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset()
      this.open = true
      this.title = "添加借还记录"
    },
    /** 修改按钮操作 */
    handleUpdate (row) {
      this.reset()
      const id = row.id || this.ids
      getRecord(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改借还记录"
      })
    },
    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除借还记录编号为"' + ids + '"的数据项？').then(function () {
        return delRecord(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => { })
    },
    /** 导出按钮操作 */
    handleExport () {
      this.download('medicine/record/export', {
        ...this.queryParams
      }, `record_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
